import React, { useState, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  ConditionalField,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,

} from '@/shared/components/common';
import { Controller } from 'react-hook-form';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  CreateProductDto,
  CreateProductResponse,
  ProductDto,
  DigitalProductConfig,
  ProductTypeEnum,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';

// Interface cho response từ backend khi có ảnh
interface ProductWithImagesResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
}

interface ProductWithUploadUrlsResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
  uploadUrls: {
    productId: string;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
  };
}

interface DigitalProductFormProps {
  onSubmit: (
    values: CreateProductDto
  ) => Promise<
    CreateProductResponse | ProductDto | ProductWithImagesResponse | ProductWithUploadUrlsResponse
  >;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho form values
interface DigitalProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  // Digital product specific fields (theo API structure)
  deliveryMethod: DigitalProductConfig['digitalFulfillmentFlow']['deliveryMethod'];
  deliveryTiming: DigitalProductConfig['digitalFulfillmentFlow']['deliveryTiming'];
  deliveryDelayMinutes?: number;
  accessStatus: DigitalProductConfig['digitalFulfillmentFlow']['accessStatus'];
  digitalProductType: DigitalProductConfig['digitalOutput']['outputType'];
  downloadLink?: string;
  usageInstructions?: string;
  loginInfo?: {
    username: string;
    password: string;
  };
}

/**
 * Form tạo sản phẩm số
 */
const DigitalProductForm: React.FC<DigitalProductFormProps> = ({ onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation(['business', 'common']);

  // Schema validation cho sản phẩm số
  const digitalProductSchema = z
    .object({
      name: z.string().min(1, 'Tên sản phẩm không được để trống'),
      typePrice: z.nativeEnum(PriceTypeEnum, {
        errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
      }),
      listPrice: z.union([z.string(), z.number()]).optional(),
      salePrice: z.union([z.string(), z.number()]).optional(),
      currency: z.string().optional(),
      priceDescription: z.string().optional(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      // Digital product specific validations (theo API structure)
      deliveryMethod: z.enum(['email', 'dashboard_download', 'sms', 'direct_message', 'zalo', 'course_activation']),
      deliveryTiming: z.enum(['immediate', 'delayed']),
      deliveryDelayMinutes: z.number().min(0).optional(),
      accessStatus: z.enum(['pending', 'delivered', 'not_delivered', 'delivery_error']),
      digitalProductType: z.enum(['online_course', 'file_download', 'license_key', 'ebook']),
      downloadLink: z.string().optional(),
      usageInstructions: z.string().optional(),
      loginInfo: z.object({
        username: z.string(),
        password: z.string(),
      }).optional(),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra giá phù hợp với loại giá
      if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
        if (!data.listPrice || data.listPrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá niêm yết',
            path: ['listPrice'],
          });
        }
        if (!data.salePrice || data.salePrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá bán',
            path: ['salePrice'],
          });
        }
        if (!data.currency || data.currency.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng chọn đơn vị tiền tệ',
            path: ['currency'],
          });
        }
      } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
        if (!data.priceDescription || !data.priceDescription.trim()) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập mô tả giá',
            path: ['priceDescription'],
          });
        }
      }

      // Kiểm tra thông tin đăng nhập cho khóa học online
      if (data.digitalProductType === 'online_course' && (!data.loginInfo || !data.loginInfo.username || !data.loginInfo.password)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập thông tin đăng nhập cho khóa học online',
          path: ['loginInfo'],
        });
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    console.log('🚀 DigitalProductForm handleSubmit called with values:', values);

    if (!values.name || !values.typePrice) {
      console.error('❌ Missing required fields:', {
        name: values.name,
        typePrice: values.typePrice,
      });
      NotificationUtil.error({
        message: 'Vui lòng nhập tên sản phẩm và chọn loại giá',
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as DigitalProductFormValues;
      setIsUploading(true);

      console.log('✅ Form values before processing:', formValues);

      // Chuyển đổi giá trị form thành dữ liệu API
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        console.error('❌ Price validation error:', priceError);
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Tạo advancedInfo theo API structure cho DIGITAL product
      const advancedInfo = {
        purchaseCount: 0,
        digitalFulfillmentFlow: {
          deliveryMethod: formValues.deliveryMethod || 'dashboard_download',
          deliveryTiming: formValues.deliveryTiming || 'immediate',
          deliveryDelayMinutes: formValues.deliveryDelayMinutes || 0,
          accessStatus: formValues.accessStatus || 'pending',
        },
        digitalOutput: {
          outputType: formValues.digitalProductType || 'online_course',
          accessLink: formValues.downloadLink || 'https://course.example.com/activate?token=abc123',
          loginInfo: formValues.loginInfo || {
            username: 'auto_generated',
            password: 'temp_password',
          },
          usageInstructions: formValues.usageInstructions || 'Vui lòng đăng nhập bằng thông tin được cung cấp để truy cập khóa học',
        },
      };

      const productData: CreateProductDto = {
        name: formValues.name,
        productType: ProductTypeEnum.DIGITAL,
        typePrice: formValues.typePrice,
        price: priceData,
        description: formValues.description || undefined,
        tags: formValues.tags && formValues.tags.length > 0 ? formValues.tags : undefined,
        imagesMediaTypes:
          mediaFiles.length > 0 ? mediaFiles.map(file => file.file.type) : undefined,
        customFields:
          productCustomFields.length > 0
            ? productCustomFields.map(field => ({
                customFieldId: field.fieldId,
                value: field.value,
              }))
            : undefined,
        advancedInfo,
      };

      console.log('📤 Final digital product data to be sent to API:', JSON.stringify(productData, null, 2));

      // Gọi callback onSubmit để parent component xử lý API call và nhận response
      const response = await onSubmit(productData);

      console.log('✅ Digital product created successfully:', response);

      // Upload media nếu có và API trả về images với upload URLs
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray(response.uploadUrls.imagesUploadUrls);

          if (hasUploadUrls) {
            const uploadUrls = response.uploadUrls.imagesUploadUrls;

            if (uploadUrls.length > 0) {
              console.log('🚀 Starting image upload with TaskQueue...');

              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index];
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo.url,
                  key: uploadInfo.key,
                  index: uploadInfo.index,
                };
              });

              // Upload tất cả ảnh cùng lúc với Promise.all
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              console.log('✅ All digital product images uploaded successfully');

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            }
          } else {
            console.warn('⚠️ Media files exist but no upload URLs provided from backend');
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch (uploadError) {
          console.error('❌ Error uploading digital product images:', uploadError);
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);
    } catch (error) {
      console.error('Error in DigitalProductForm handleSubmit:', error);
      setIsUploading(false);

      NotificationUtil.error({
        message: t('business:product.createError'),
        duration: 3000,
      });
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = (values: DigitalProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE) {
      if (!values.listPrice || values.listPrice === '') {
        throw new Error('Vui lòng nhập giá niêm yết');
      }
      if (!values.salePrice || values.salePrice === '') {
        throw new Error('Vui lòng nhập giá bán');
      }
      if (!values.currency || values.currency.trim() === '') {
        throw new Error('Vui lòng chọn đơn vị tiền tệ');
      }

      const listPrice = Number(values.listPrice);
      const salePrice = Number(values.salePrice);

      if (isNaN(listPrice) || listPrice < 0) {
        throw new Error('Giá niêm yết phải là số >= 0');
      }
      if (isNaN(salePrice) || salePrice < 0) {
        throw new Error('Giá bán phải là số >= 0');
      }
      if (listPrice <= salePrice) {
        throw new Error('Giá niêm yết phải lớn hơn giá bán');
      }

      return {
        listPrice,
        salePrice,
        currency: values.currency.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!values.priceDescription || !values.priceDescription.trim()) {
        throw new Error('Vui lòng nhập mô tả giá');
      }
      return {
        priceDescription: values.priceDescription.trim(),
      };
    }

    throw new Error('Loại giá không hợp lệ');
  };

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.label as string) || `Field ${fieldId}`,
          component: (fieldData?.component as string) || (fieldData?.type as string) || 'text',
          type: (fieldData?.type as string) || 'text',
          required: (fieldData?.required as boolean) || false,
          configJson: (fieldData?.configJson as Record<string, unknown>) || {},
          value: { value: '' },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Giá trị mặc định cho form
  const defaultValues = useMemo(
    () => ({
      name: '',
      typePrice: PriceTypeEnum.HAS_PRICE,
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      priceDescription: '',
      description: '',
      tags: [],
      customFields: [],
      media: [],
      // Digital product defaults (theo API structure)
      deliveryMethod: 'dashboard_download' as const,
      deliveryTiming: 'immediate' as const,
      deliveryDelayMinutes: 0,
      accessStatus: 'pending' as const,
      digitalProductType: 'online_course' as const,
      downloadLink: '',
      usageInstructions: '',
      loginInfo: {
        username: 'auto_generated',
        password: 'temp_password',
      },
    }),
    []
  );

  return (
    <FormMultiWrapper title={t('business:product.form.createDigitalTitle', 'Tạo sản phẩm số')}>
      <Form
        ref={formRef}
        schema={digitalProductSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          console.error('🔥 Form validation errors:', errors);
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';
          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.descriptionPlaceholder')}
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('business:product.form.tagsPlaceholder')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();
                          const newTag = e.currentTarget.value.trim();
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Giá sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.pricing', '2. Giá sản phẩm')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="typePrice" label={t('business:product.priceType.title')} required>
              <Select
                fullWidth
                options={[
                  {
                    value: PriceTypeEnum.HAS_PRICE,
                    label: t('business:product.priceType.hasPrice'),
                  },
                  {
                    value: PriceTypeEnum.STRING_PRICE,
                    label: t('business:product.priceType.stringPrice'),
                  },
                ]}
              />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.HAS_PRICE,
              }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem name="listPrice" label={t('business:product.listPrice')} required>
                  <Input fullWidth type="number" min="0" placeholder="Nhập giá niêm yết" />
                </FormItem>
                <FormItem name="salePrice" label={t('business:product.salePrice')} required>
                  <Input fullWidth type="number" min="0" placeholder="Nhập giá bán" />
                </FormItem>
                <FormItem name="currency" label={t('business:product.currency')} required>
                  <Controller
                    name="currency"
                    render={({ field }) => (
                      <Select
                        fullWidth
                        value={field.value || 'VND'}
                        onChange={value => field.onChange(value)}
                        options={[
                          { value: 'VND', label: 'VND' },
                          { value: 'USD', label: 'USD' },
                          { value: 'EUR', label: 'EUR' },
                        ]}
                      />
                    )}
                  />
                </FormItem>
              </div>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.STRING_PRICE,
              }}
            >
              <FormItem
                name="priceDescription"
                label={t('business:product.priceDescription')}
                required
              >
                <Input
                  fullWidth
                  placeholder={t('business:product.form.priceDescriptionPlaceholder')}
                />
              </FormItem>
            </ConditionalField>
          </div>
        </CollapsibleCard>

        {/* 3. Quy trình xử lý đơn hàng số */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.digitalProcessing', '3. Quy trình xử lý đơn hàng số')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="deliveryMethod" label="Cách giao hàng" required>
              <Select
                fullWidth
                options={[
                  { value: 'email', label: 'Email' },
                  { value: 'dashboard_download', label: 'Tải về từ dashboard' },
                  { value: 'sms', label: 'SMS' },
                  { value: 'direct_message', label: 'Tin nhắn trực tiếp' },
                  { value: 'zalo', label: 'Zalo' },
                  { value: 'course_activation', label: 'Kích hoạt khóa học' },
                ]}
              />
            </FormItem>

            <FormItem name="deliveryTiming" label="Thời điểm giao hàng" required>
              <Select
                fullWidth
                options={[
                  { value: 'immediate', label: 'Ngay sau thanh toán' },
                  { value: 'delayed', label: 'Có thời gian chờ' },
                ]}
              />
            </FormItem>

            {/* Thời gian chờ - chỉ hiển thị khi deliveryTiming = 'delayed' */}
            <ConditionalField
              condition={{
                field: 'deliveryTiming',
                type: ConditionType.EQUALS,
                value: 'delayed',
              }}
            >
              <FormItem name="deliveryDelayMinutes" label="Thời gian chờ (phút)">
                <Input fullWidth type="number" min="0" placeholder="Nhập số phút chờ" />
              </FormItem>
            </ConditionalField>

            <FormItem name="accessStatus" label="Tình trạng truy cập" required>
              <Select
                fullWidth
                options={[
                  { value: 'pending', label: 'Đang chờ' },
                  { value: 'delivered', label: 'Đã giao' },
                  { value: 'not_delivered', label: 'Chưa giao' },
                  { value: 'delivery_error', label: 'Lỗi giao hàng' },
                ]}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 4. Đầu ra sản phẩm số */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.digitalOutput', '4. Đầu ra sản phẩm số')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="digitalProductType" label="Loại sản phẩm số" required>
              <Select
                fullWidth
                options={[
                  { value: 'online_course', label: 'Khóa học online' },
                  { value: 'file_download', label: 'File download' },
                  { value: 'license_key', label: 'Mã bản quyền' },
                  { value: 'ebook', label: 'E-book' },
                ]}
              />
            </FormItem>

            <FormItem name="downloadLink" label="Link truy cập">
              <Input fullWidth placeholder="Nhập link truy cập sản phẩm số" />
            </FormItem>

            {/* Thông tin đăng nhập - chỉ hiển thị cho khóa học online */}
            <ConditionalField
              condition={{
                field: 'digitalProductType',
                type: ConditionType.EQUALS,
                value: 'online_course',
              }}
            >
              <div className="space-y-4">
                <Typography variant="body2" className="font-medium">
                  Thông tin đăng nhập khóa học:
                </Typography>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem name="loginInfo.username" label="Tên đăng nhập" required>
                    <Input fullWidth placeholder="Nhập tên đăng nhập" />
                  </FormItem>
                  <FormItem name="loginInfo.password" label="Mật khẩu" required>
                    <Input fullWidth type="password" placeholder="Nhập mật khẩu" />
                  </FormItem>
                </div>
              </div>
            </ConditionalField>

            <FormItem name="usageInstructions" label="Hướng dẫn sử dụng">
              <Textarea
                fullWidth
                rows={4}
                placeholder="Nhập hướng dẫn chi tiết cách sử dụng sản phẩm số"
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 5. Hình ảnh sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.media', '5. Hình ảnh sản phẩm')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="media" label={t('business:product.form.media')}>
              <Controller
                name="media"
                render={({ field }) => (
                  <MultiFileUpload
                    value={mediaFiles}
                    onChange={(files: FileWithMetadata[]) => {
                      setMediaFiles(files);
                      field.onChange(files);
                    }}
                    accept="image/*"
                    mediaOnly={true}
                    placeholder={t(
                      'business:product.form.mediaPlaceholder',
                      'Kéo thả hoặc click để tải lên ảnh/video'
                    )}
                    className="w-full"
                  />
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 6. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '6. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(field => field.fieldId)}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-4 mt-4">
                <Typography variant="body2" className="font-medium">
                  {t('business:product.form.selectedCustomFields', 'Trường tùy chỉnh đã chọn:')}
                </Typography>
                {productCustomFields.map(field => (
                  <CustomFieldRenderer
                    key={field.id}
                    field={field}
                    value={field.value as unknown as string | number | boolean}
                    onChange={(value: string | number | boolean) =>
                      handleUpdateCustomFieldInProduct(field.id, String(value))
                    }
                    onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="check"
            title={
              isSubmitting || isUploading
                ? t('business:product.form.creating', 'Đang tạo...')
                : t('business:product.form.create', 'Tạo sản phẩm')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={isSubmitting || isUploading}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default DigitalProductForm;
