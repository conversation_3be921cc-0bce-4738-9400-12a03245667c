import { ReactNode, useEffect, useRef, useState } from 'react';

type TooltipPosition = 'top' | 'right' | 'bottom' | 'left';

interface TooltipProps {
  /**
   * Nội dung bên trong tooltip
   */
  children: ReactNode;

  /**
   * Nội dung hiển thị trong tooltip
   */
  content: ReactNode;

  /**
   * Vị trí hiển thị tooltip
   * @default 'top'
   */
  position?: TooltipPosition;

  /**
   * Độ trễ hiển thị tooltip (ms)
   * @default 150
   */
  delay?: number;

  /**
   * Class bổ sung cho tooltip
   */
  className?: string;

  /**
   * C<PERSON> hiển thị mũi tên không
   * @default true
   */
  arrow?: boolean;

  /**
   * K<PERSON>ch thước của tooltip
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Component Tooltip hiển thị tooltip với thiết kế hiện đại
 */
const Tooltip = ({
  children,
  content,
  position = 'top',
  delay = 150,
  className = '',
  arrow = true,
  size = 'md',
}: TooltipProps) => {
  const [isVisible, setIsVisible] = useState(false);
  // Removed unused coords state
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<number | null>(null);

  // Position classes
  const positionClasses = {
    top: 'bottom-full left-1/2 -translate-x-1/2 mb-2',
    right: 'left-full top-1/2 -translate-y-1/2 ml-2',
    bottom: 'top-full left-1/2 -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 -translate-y-1/2 mr-2',
  };

  // Arrow classes
  const arrowClasses = {
    top: 'bottom-[-6px] left-1/2 -translate-x-1/2 border-t-gray-800 dark:border-t-gray-700 border-l-transparent border-r-transparent border-b-transparent',
    right:
      'left-[-6px] top-1/2 -translate-y-1/2 border-r-gray-800 dark:border-r-gray-700 border-t-transparent border-b-transparent border-l-transparent',
    bottom:
      'top-[-6px] left-1/2 -translate-x-1/2 border-b-gray-800 dark:border-b-gray-700 border-l-transparent border-r-transparent border-t-transparent',
    left: 'right-[-6px] top-1/2 -translate-y-1/2 border-l-gray-800 dark:border-l-gray-700 border-t-transparent border-b-transparent border-r-transparent',
  };

  // Calculate position
  const calculatePosition = () => {
    // No need to set coords anymore, just a placeholder for resize/scroll events
    if (!triggerRef.current) {return;}
    // Position is handled by CSS
  };

  // Handle mouse enter
  const handleMouseEnter = () => {
    // Nếu đang có timer, xóa timer cũ
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    // Tính toán vị trí
    calculatePosition();

    // Đặt timer mới để hiển thị tooltip
    timerRef.current = window.setTimeout(() => {
      setIsVisible(true);
    }, delay);
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    // Xóa timer nếu có
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    // Ẩn tooltip ngay lập tức
    setIsVisible(false);
  };

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // Update position on window resize
  useEffect(() => {
    if (isVisible) {
      window.addEventListener('resize', calculatePosition);
      window.addEventListener('scroll', calculatePosition);
    }

    return () => {
      window.removeEventListener('resize', calculatePosition);
      window.removeEventListener('scroll', calculatePosition);
    };
  }, [isVisible]);

  return (
    <div className="relative inline-block">
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="inline-block"
      >
        {children}
      </div>

      {isVisible && (
        <div
          ref={tooltipRef}
          className={`fixed z-[9999] ${positionClasses[position]} animate-fade-in duration-200 ${className}`}
          style={{
            left: triggerRef.current
              ? position === 'right'
                ? triggerRef.current.getBoundingClientRect().right
                : undefined
              : undefined,
            top: triggerRef.current
              ? position === 'right'
                ? triggerRef.current.getBoundingClientRect().top +
                  triggerRef.current.offsetHeight / 2
                : undefined
              : undefined,
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div
            className={`bg-gray-800 dark:bg-gray-700 text-white rounded whitespace-nowrap
            ${size === 'sm' ? 'text-xs py-1 px-2' : size === 'md' ? 'text-sm py-1.5 px-3' : 'text-base py-2 px-4'}`}
          >
            {content}
            {arrow && <div className={`absolute border-4 ${arrowClasses[position]}`} />}
          </div>
        </div>
      )}
    </div>
  );
};

export default Tooltip;
